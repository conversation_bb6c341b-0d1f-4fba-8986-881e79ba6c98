# Kritrima AI CLI - Comprehensive Python System Integration Plan 

## Table of Contents
1. [System Architecture Overview](#system-architecture-overview)
2. [Provider & LLM Integration System](#provider--llm-integration-system)
3. [Autonomous Agent Loop & Tool Calling](#autonomous-agent-loop--tool-calling)
4. [Advanced Code Assistance System](#advanced-code-assistance-system)
5. [Multi-Modal AI Interaction](#multi-modal-ai-interaction)
6. [Configuration & State Management](#configuration--state-management)
7. [Security & Approval System](#security--approval-system)
8. [User Interface & Experience](#user-interface--experience)
9. [Data Flow & Integration Points](#data-flow--integration-points)
10. [Error Handling & Resilience](#error-handling--resilience)
11. [Advanced Storage & Session Management](#advanced-storage--session-management)
12. [Logging & Debugging Infrastructure](#logging--debugging-infrastructure)
13. [Bug Reporting & Telemetry System](#bug-reporting--telemetry-system)
14. [Full-Context & Single-Pass Mode](#full-context--single-pass-mode)
15. [Command History & User Experience](#command-history--user-experience)
16. [File Operations & Tag System](#file-operations--tag-system)
17. [Custom Components & UI Framework](#custom-components--ui-framework)
18. [Third-Party Integration & Libraries](#third-party-integration--libraries)
19. [Text Processing & Advanced Input System](#text-processing--advanced-input-system)
20. [Example Projects & Templates](#example-projects--templates)
21. [Advanced Features & Integrations](#advanced-features--integrations)

---

## System Architecture Overview

The Kritrima AI CLI is built as a sophisticated, multi-layered Python system with the following core components:

```
┌─────────────────────────────────────────────────────────────┐
│                    CLI Entry Point                          │
│                  (bin/kritrima-ai)                         │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                Main CLI Application                         │
│                   (src/cli.py)                             │
│  • Argument parsing • Config loading • Mode selection      │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                 Application Layer                           │
│                  (src/app.py)                              │
│  • Git validation • Safety checks • UI orchestration      │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│              Terminal Chat Interface                        │
│           (src/components/chat/terminal_chat.py)           │
│  • Real-time conversation • Model switching • Overlays     │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                Agent Loop Core                              │
│           (src/utils/agent/agent_loop.py)                  │
│  • AI orchestration • Tool calling • State management      │
└─────────────────────────────────────────────────────────────┘
```

### Python Technology Stack

**Core Framework:**
- **Python 3.8+**: Modern Python with type hints and async support
- **Click**: Modern CLI framework for command-line interfaces
- **Rich**: Advanced terminal UI library with rich text and layouts
- **Textual**: Modern TUI framework for complex terminal applications
- **AsyncIO**: Asynchronous programming for concurrent operations

**Package Management:**
- **Poetry**: Modern dependency management and packaging
- **pyproject.toml**: Modern Python project configuration
- **Virtual Environment**: Isolated dependency management

**Entry Point Structure:**
```python
# pyproject.toml
[tool.poetry.scripts]
kritrima-ai = "kritrima_ai.cli:main"

# src/kritrima_ai/cli.py
import click
from rich.console import Console
from textual.app import App

@click.command()
@click.option('--model', help='AI model to use')
@click.option('--provider', help='AI provider')
def main(model: str, provider: str):
    """Kritrima AI CLI - Advanced AI-powered development assistant"""
    app = KritrimaApp(model=model, provider=provider)
    app.run()
```

---

## Provider & LLM Integration System

### 1. Provider Configuration Architecture

**File: `src/utils/providers.py`**
- Defines supported AI providers with their configurations
- Built-in providers: OpenAI, Azure, Gemini, Ollama, Mistral, DeepSeek, xAI, Groq, ArceeAI, OpenRouter
- Each provider has: `name`, `base_url`, `env_key` for API authentication

```python
from dataclasses import dataclass
from typing import Dict, Optional
import os

@dataclass
class ProviderConfig:
    name: str
    base_url: str
    env_key: str
    default_model: Optional[str] = None

PROVIDERS: Dict[str, ProviderConfig] = {
    "openai": ProviderConfig(
        name="OpenAI", 
        base_url="https://api.openai.com/v1", 
        env_key="OPENAI_API_KEY",
        default_model="gpt-4"
    ),
    "ollama": ProviderConfig(
        name="Ollama", 
        base_url="http://localhost:11434/v1", 
        env_key="OLLAMA_API_KEY",
        default_model="llama2"
    ),
    # ... other providers
}
```

### 2. Dynamic Provider Resolution

**File: `src/utils/config.py`**
- `get_base_url()`: Resolves provider base URL with environment variable override support
- `get_api_key()`: Retrieves API keys with fallback mechanisms
- Supports custom providers via environment variables (`PROVIDER_API_KEY`, `PROVIDER_BASE_URL`)

```python
from typing import Optional
import os
from .providers import PROVIDERS, ProviderConfig

def get_base_url(provider: str) -> str:
    """Get base URL for provider with environment override support"""
    env_override = os.getenv(f"{provider.upper()}_BASE_URL")
    if env_override:
        return env_override
    
    provider_config = PROVIDERS.get(provider.lower())
    if provider_config:
        return provider_config.base_url
    
    raise ValueError(f"Unknown provider: {provider}")

def get_api_key(provider: str = "openai") -> Optional[str]:
    """Retrieve API key with fallback mechanisms"""
    provider_config = PROVIDERS.get(provider.lower())
    
    if provider_config:
        return os.getenv(provider_config.env_key)
    
    # Fallback to custom environment variable
    return os.getenv(f"{provider.upper()}_API_KEY")
```

### 3. OpenAI Client Factory

**File: `src/utils/openai_client.py`**
- `create_openai_client()`: Creates provider-specific client instances
- Handles both standard OpenAI and Azure OpenAI configurations
- Applies timeout, organization, and project headers

```python
from openai import OpenAI, AzureOpenAI
from typing import Optional
import httpx
from .config import get_base_url, get_api_key

def create_openai_client(
    provider: str = "openai",
    timeout: int = 60,
    organization: Optional[str] = None,
    project: Optional[str] = None
) -> OpenAI:
    """Create provider-specific OpenAI client instance"""
    
    base_url = get_base_url(provider)
    api_key = get_api_key(provider)
    
    if not api_key:
        raise ValueError(f"API key not found for provider: {provider}")
    
    # Handle Azure OpenAI special case
    if provider == "azure":
        return AzureOpenAI(
            api_key=api_key,
            api_version="2024-02-01",
            azure_endpoint=base_url,
            timeout=timeout
        )
    
    # Standard OpenAI-compatible client
    return OpenAI(
        api_key=api_key,
        base_url=base_url,
        timeout=timeout,
        organization=organization,
        default_headers={
            "OpenAI-Project": project
        } if project else None
    )
```

### 4. Model Management System

**File: `src/utils/model_utils.py`**
- `fetch_models()`: Dynamically retrieves available models from providers
- Background caching for performance using `asyncio` and `aiofiles`
- Model validation and compatibility checking
- Context length calculation and management

```python
import asyncio
import aiofiles
import json
from typing import List, Dict, Optional
from pathlib import Path
from .openai_client import create_openai_client

class ModelManager:
    def __init__(self, cache_dir: Path = Path.home() / ".kritrima-ai" / "cache"):
        self.cache_dir = cache_dir
        self.cache_dir.mkdir(parents=True, exist_ok=True)
    
    async def fetch_models(self, provider: str) -> List[str]:
        """Dynamically retrieve available models from providers"""
        cache_file = self.cache_dir / f"{provider}_models.json"
        
        # Try cache first
        if cache_file.exists():
            async with aiofiles.open(cache_file, 'r') as f:
                cached_data = json.loads(await f.read())
                if self._is_cache_valid(cached_data):
                    return cached_data['models']
        
        # Fetch from provider
        try:
            client = create_openai_client(provider)
            models_response = client.models.list()
            models = [model.id for model in models_response.data]
            models.sort()
            
            # Cache results
            cache_data = {
                'models': models,
                'timestamp': asyncio.get_event_loop().time()
            }
            async with aiofiles.open(cache_file, 'w') as f:
                await f.write(json.dumps(cache_data, indent=2))
            
            return models
        except Exception as e:
            # Return empty list on error, let UI handle gracefully
            return []
    
    def _is_cache_valid(self, cached_data: Dict, max_age: int = 3600) -> bool:
        """Check if cached data is still valid (default 1 hour)"""
        current_time = asyncio.get_event_loop().time()
        return (current_time - cached_data.get('timestamp', 0)) < max_age
```

**File: `src/utils/model_info.py`**
- Comprehensive model metadata (context lengths, capabilities)
- Support for latest models (o1, o3, GPT-4.1, etc.)

```python
from dataclasses import dataclass
from typing import Dict, Optional, List

@dataclass
class ModelInfo:
    context_length: int
    supports_function_calling: bool = True
    supports_vision: bool = False
    supports_streaming: bool = True
    max_output_tokens: Optional[int] = None
    cost_per_1k_input: Optional[float] = None
    cost_per_1k_output: Optional[float] = None

MODEL_INFO: Dict[str, ModelInfo] = {
    "gpt-4": ModelInfo(
        context_length=8192,
        supports_function_calling=True,
        supports_vision=False,
        cost_per_1k_input=0.03,
        cost_per_1k_output=0.06
    ),
    "gpt-4-vision-preview": ModelInfo(
        context_length=128000,
        supports_function_calling=True,
        supports_vision=True,
        cost_per_1k_input=0.01,
        cost_per_1k_output=0.03
    ),
    "o1-preview": ModelInfo(
        context_length=128000,
        supports_function_calling=False,
        supports_streaming=False,
        max_output_tokens=32768,
        cost_per_1k_input=0.015,
        cost_per_1k_output=0.06
    ),
    # ... other models
}

def get_model_info(model: str) -> ModelInfo:
    """Get model information with fallback to defaults"""
    return MODEL_INFO.get(model, ModelInfo(context_length=4096))
```

### 5. Provider Switching Workflow

**Component: `src/components/model_overlay.py`**
1. User triggers model/provider overlay (`/model` command or hotkey)
2. System fetches available models for current provider
3. Tab navigation between provider and model selection using Rich/Textual widgets
4. Real-time model list updates when provider changes
5. Configuration persistence and session management

```python
from textual.app import ComposeResult
from textual.containers import Container, Horizontal, Vertical
from textual.widgets import Select, Button, Label
from textual.screen import ModalScreen
from typing import List, Callable

class ModelOverlay(ModalScreen):
    """Model and provider selection overlay"""

    def __init__(self,
                 current_provider: str,
                 current_model: str,
                 on_selection: Callable[[str, str], None]):
        super().__init__()
        self.current_provider = current_provider
        self.current_model = current_model
        self.on_selection = on_selection
        self.model_manager = ModelManager()

    def compose(self) -> ComposeResult:
        with Container(id="model-overlay"):
            with Vertical():
                yield Label("Select AI Provider and Model", id="title")

                with Horizontal():
                    yield Select(
                        options=[(name, key) for key, name in
                                [(k, v.name) for k, v in PROVIDERS.items()]],
                        value=self.current_provider,
                        id="provider-select"
                    )
                    yield Select(
                        options=[],  # Will be populated dynamically
                        value=self.current_model,
                        id="model-select"
                    )

                with Horizontal():
                    yield Button("Apply", variant="primary", id="apply")
                    yield Button("Cancel", variant="default", id="cancel")

    async def on_select_changed(self, event: Select.Changed) -> None:
        """Handle provider selection change"""
        if event.select.id == "provider-select":
            # Fetch models for new provider
            models = await self.model_manager.fetch_models(event.value)
            model_select = self.query_one("#model-select", Select)
            model_select.set_options([(model, model) for model in models])

    def on_button_pressed(self, event: Button.Pressed) -> None:
        """Handle button clicks"""
        if event.button.id == "apply":
            provider = self.query_one("#provider-select", Select).value
            model = self.query_one("#model-select", Select).value
            self.on_selection(provider, model)
            self.dismiss()
        elif event.button.id == "cancel":
            self.dismiss()
```

---

## Autonomous Agent Loop & Tool Calling

### 1. Core Agent Architecture

**File: `src/utils/agent/agent_loop.py`**

The agent loop is the heart of the AI system, implementing a sophisticated autonomous workflow using Python's async capabilities:

```python
import asyncio
from typing import List, Dict, Any, Optional, Set, AsyncGenerator
from dataclasses import dataclass, field
from enum import Enum
import json
from openai import OpenAI
from openai.types.chat import ChatCompletion, ChatCompletionChunk

class ApprovalPolicy(Enum):
    SUGGEST = "suggest"
    AUTO_EDIT = "auto_edit"
    FULL_AUTO = "full_auto"

@dataclass
class ResponseInputItem:
    role: str
    content: List[Dict[str, Any]]
    type: str = "message"

@dataclass
class AgentConfig:
    model: str
    provider: str
    approval_policy: ApprovalPolicy
    max_iterations: int = 10
    timeout: int = 300

class AgentLoop:
    """Core agent loop for AI orchestration and tool calling"""

    def __init__(self, config: AgentConfig):
        self.config = config
        self.client = create_openai_client(config.provider)
        self.transcript: List[ResponseInputItem] = []
        self.pending_aborts: Set[str] = set()
        self.cumulative_thinking_ms = 0

    async def run(self,
                  initial_prompt: str,
                  context_files: Optional[List[str]] = None) -> AsyncGenerator[Dict[str, Any], None]:
        """Main agent loop execution"""

        # Phase 1: Request Processing
        input_item = await self._create_input_item(initial_prompt, context_files)
        self.transcript.append(input_item)

        iteration = 0
        while iteration < self.config.max_iterations:
            try:
                # Phase 2: AI Response Handling
                async for event in self._stream_ai_response():
                    yield event

                    if event.get("type") == "function_call":
                        # Phase 3: Tool Execution
                        tool_results = await self._handle_function_call(event["data"])

                        # Phase 4: Conversation Continuation
                        self.transcript.extend(tool_results)

                        # Continue loop for next iteration
                        break
                    elif event.get("type") == "response_complete":
                        # No more tool calls, conversation complete
                        return

                iteration += 1

            except asyncio.TimeoutError:
                yield {"type": "error", "message": "Request timeout"}
                break
            except Exception as e:
                yield {"type": "error", "message": str(e)}
                break

    async def _stream_ai_response(self) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream AI response with real-time processing"""

        messages = self._build_conversation_context()
        tools = self._get_available_tools()

        try:
            stream = await self.client.chat.completions.create(
                model=self.config.model,
                messages=messages,
                tools=tools,
                stream=True,
                timeout=self.config.timeout
            )

            current_function_call = None

            async for chunk in stream:
                if chunk.choices and chunk.choices[0].delta:
                    delta = chunk.choices[0].delta

                    # Handle text content
                    if delta.content:
                        yield {
                            "type": "text_delta",
                            "content": delta.content
                        }

                    # Handle function calls
                    if delta.tool_calls:
                        for tool_call in delta.tool_calls:
                            if tool_call.function:
                                if current_function_call is None:
                                    current_function_call = {
                                        "id": tool_call.id,
                                        "name": tool_call.function.name,
                                        "arguments": ""
                                    }

                                if tool_call.function.arguments:
                                    current_function_call["arguments"] += tool_call.function.arguments

                    # Check for completion
                    if chunk.choices[0].finish_reason == "tool_calls" and current_function_call:
                        yield {
                            "type": "function_call",
                            "data": current_function_call
                        }
                        current_function_call = None
                    elif chunk.choices[0].finish_reason == "stop":
                        yield {"type": "response_complete"}

        except Exception as e:
            yield {"type": "error", "message": f"AI response error: {str(e)}"}
```

### 2. Tool Calling System

#### Available Tools:
1. **Shell Tool** (`shell`): Execute system commands using `subprocess`
2. **File Operations Tool** (`file_ops`): File modification operations
3. **Git Tool** (`git`): Git repository operations

#### Tool Definition:
```python
from typing import Dict, Any, List
import subprocess
import asyncio

@dataclass
class ToolDefinition:
    name: str
    description: str
    parameters: Dict[str, Any]

def get_shell_tool() -> ToolDefinition:
    """Define shell command execution tool"""
    return ToolDefinition(
        name="shell",
        description="Runs a shell command and returns its output.",
        parameters={
            "type": "object",
            "properties": {
                "command": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Command and arguments to execute"
                },
                "workdir": {
                    "type": "string",
                    "description": "Working directory for command execution"
                },
                "timeout": {
                    "type": "number",
                    "description": "Timeout in seconds",
                    "default": 30
                }
            },
            "required": ["command"]
        }
    )

async def execute_shell_command(
    command: List[str],
    workdir: Optional[str] = None,
    timeout: int = 30
) -> Dict[str, Any]:
    """Execute shell command with proper error handling"""

    try:
        process = await asyncio.create_subprocess_exec(
            *command,
            cwd=workdir,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
            limit=1024*1024  # 1MB limit
        )

        stdout, stderr = await asyncio.wait_for(
            process.communicate(),
            timeout=timeout
        )

        return {
            "success": True,
            "stdout": stdout.decode('utf-8', errors='replace'),
            "stderr": stderr.decode('utf-8', errors='replace'),
            "return_code": process.returncode
        }

    except asyncio.TimeoutError:
        return {
            "success": False,
            "error": f"Command timed out after {timeout} seconds"
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }
```

### 3. Agent Loop Execution Flow

#### Phase 1: Request Processing
1. **Input Validation**: Validate and sanitize user input using Pydantic
2. **Context Assembly**: Build conversation context with history
3. **Tool Registration**: Register available tools based on approval policy
4. **Request Preparation**: Format request for AI provider

#### Phase 2: AI Response Handling
1. **Streaming Response**: Process real-time AI responses using `asyncio`
2. **Tool Call Detection**: Identify function calls in response
3. **Tool Call Parsing**: Extract command details and parameters using JSON parsing
4. **Approval Workflow**: Route through approval system if required

#### Phase 3: Tool Execution
1. **Command Validation**: Security checks and approval using custom validators
2. **Sandbox Execution**: Run commands in secure environment using `subprocess`
3. **Output Capture**: Collect and format execution results
4. **Response Assembly**: Prepare results for next AI iteration

#### Phase 4: Conversation Continuation
1. **Context Update**: Add results to conversation history
2. **Loop Decision**: Determine if more iterations needed
3. **State Persistence**: Save conversation state using `pickle` or JSON
4. **UI Update**: Refresh interface with new information using Rich/Textual

### 4. Function Call Processing

**Method: `_handle_function_call()`**
```python
async def _handle_function_call(self, function_call: Dict[str, Any]) -> List[ResponseInputItem]:
    """Handle function call execution with approval workflow"""

    function_name = function_call["name"]
    arguments_str = function_call["arguments"]

    try:
        arguments = json.loads(arguments_str)
    except json.JSONDecodeError as e:
        return [self._create_error_response(f"Invalid function arguments: {e}")]

    if function_name == "shell":
        return await self._handle_shell_command(arguments)
    elif function_name == "file_ops":
        return await self._handle_file_operations(arguments)
    elif function_name == "git":
        return await self._handle_git_command(arguments)
    else:
        return [self._create_error_response(f"Unknown function: {function_name}")]

async def _handle_shell_command(self, args: Dict[str, Any]) -> List[ResponseInputItem]:
    """Handle shell command execution with approval"""

    command = args.get("command", [])
    workdir = args.get("workdir")
    timeout = args.get("timeout", 30)

    # Check approval policy
    if not await self._check_command_approval(command):
        return [self._create_approval_required_response(command)]

    # Execute command
    result = await execute_shell_command(command, workdir, timeout)

    # Format response
    if result["success"]:
        content = f"Command executed successfully:\n"
        content += f"Exit code: {result['return_code']}\n"
        if result["stdout"]:
            content += f"Output:\n{result['stdout']}\n"
        if result["stderr"]:
            content += f"Errors:\n{result['stderr']}\n"
    else:
        content = f"Command failed: {result['error']}"

    return [ResponseInputItem(
        role="tool",
        content=[{"type": "text", "text": content}],
        type="tool_result"
    )]

async def _check_command_approval(self, command: List[str]) -> bool:
    """Check if command requires approval based on policy"""

    if self.config.approval_policy == ApprovalPolicy.FULL_AUTO:
        return True
    elif self.config.approval_policy == ApprovalPolicy.SUGGEST:
        return await self._request_user_approval(command)
    elif self.config.approval_policy == ApprovalPolicy.AUTO_EDIT:
        # Auto-approve safe commands, require approval for dangerous ones
        return self._is_safe_command(command) or await self._request_user_approval(command)

    return False

def _is_safe_command(self, command: List[str]) -> bool:
    """Check if command is considered safe for auto-execution"""
    safe_commands = {
        "ls", "dir", "cat", "type", "grep", "find", "head", "tail",
        "pwd", "echo", "which", "whereis", "file", "stat", "wc"
    }

    if not command:
        return False

    base_command = command[0].split("/")[-1]  # Get command name without path
    return base_command in safe_commands
```

### 5. Conversation State Management

#### Server-Side Storage (Default):
- Uses `previous_response_id` for context continuity
- Efficient for long conversations
- Requires stable connection to AI provider

#### Client-Side Storage (Fallback):
- Maintains full transcript locally using Python data structures
- Sends complete context with each request
- Resilient to connection issues using local persistence
- Higher token usage but more reliable

```python
import pickle
from pathlib import Path
from typing import Optional

class ConversationManager:
    """Manage conversation state and persistence"""

    def __init__(self, session_dir: Path = Path.home() / ".kritrima-ai" / "sessions"):
        self.session_dir = session_dir
        self.session_dir.mkdir(parents=True, exist_ok=True)
        self.use_server_side_storage = True

    def save_conversation(self, session_id: str, transcript: List[ResponseInputItem]) -> None:
        """Save conversation to local storage"""
        session_file = self.session_dir / f"{session_id}.pkl"

        try:
            with open(session_file, 'wb') as f:
                pickle.dump({
                    'transcript': transcript,
                    'timestamp': time.time(),
                    'version': '1.0'
                }, f)
        except Exception as e:
            logger.error(f"Failed to save conversation: {e}")

    def load_conversation(self, session_id: str) -> Optional[List[ResponseInputItem]]:
        """Load conversation from local storage"""
        session_file = self.session_dir / f"{session_id}.pkl"

        if not session_file.exists():
            return None

        try:
            with open(session_file, 'rb') as f:
                data = pickle.load(f)
                return data.get('transcript', [])
        except Exception as e:
            logger.error(f"Failed to load conversation: {e}")
            return None

    def build_context_messages(self, transcript: List[ResponseInputItem]) -> List[Dict[str, Any]]:
        """Build OpenAI-compatible messages from transcript"""
        messages = []

        for item in transcript:
            if item.role in ["user", "assistant", "system"]:
                message = {
                    "role": item.role,
                    "content": self._format_content(item.content)
                }
                messages.append(message)

        return messages

    def _format_content(self, content: List[Dict[str, Any]]) -> str:
        """Format content for OpenAI API"""
        if len(content) == 1 and content[0].get("type") == "text":
            return content[0]["text"]

        # Handle multi-modal content
        formatted_parts = []
        for part in content:
            if part.get("type") == "text":
                formatted_parts.append(part["text"])
            elif part.get("type") == "image":
                formatted_parts.append("[Image content]")

        return "\n".join(formatted_parts)
```

---

## Advanced Code Assistance System

### 1. File Operations System

**File: `src/utils/agent/apply_patch.py`**

#### Unified Diff Processing using Python libraries:
- **Patch Parsing**: Parse unified diff format using `difflib` and custom parsers
- **File Creation**: Create new files with content using `pathlib`
- **File Editing**: Apply incremental changes using Python file I/O
- **File Deletion**: Remove files safely with proper error handling
- **Backup System**: Automatic backup before changes using `shutil`
- **Rollback Capability**: Undo changes if needed using backup restoration

```python
import difflib
import shutil
from pathlib import Path
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
import re
import tempfile

@dataclass
class PatchOperation:
    operation: str  # 'create', 'modify', 'delete', 'move'
    file_path: Path
    content: Optional[str] = None
    backup_path: Optional[Path] = None

class PatchProcessor:
    """Advanced patch processing for file operations"""

    def __init__(self, backup_dir: Optional[Path] = None):
        self.backup_dir = backup_dir or Path(tempfile.gettempdir()) / "kritrima-backups"
        self.backup_dir.mkdir(parents=True, exist_ok=True)

    def process_patch(self, patch_content: str, base_dir: Path = Path.cwd()) -> List[PatchOperation]:
        """Process unified diff patch and return operations"""
        operations = []

        # Parse V4A diff format or standard unified diff
        if "*** [ACTION]" in patch_content:
            operations.extend(self._parse_v4a_format(patch_content, base_dir))
        else:
            operations.extend(self._parse_unified_diff(patch_content, base_dir))

        return operations

    def _parse_v4a_format(self, patch_content: str, base_dir: Path) -> List[PatchOperation]:
        """Parse V4A diff format"""
        operations = []

        # V4A format: *** [ACTION] File: [path/to/file]
        action_pattern = r'\*\*\* \[(CREATE|EDIT|DELETE|MOVE)\] File: (.+)'

        sections = re.split(action_pattern, patch_content)

        for i in range(1, len(sections), 3):
            action = sections[i]
            file_path = Path(base_dir) / sections[i + 1].strip()
            content_section = sections[i + 2] if i + 2 < len(sections) else ""

            if action == "CREATE":
                # Extract content after the file declaration
                content = self._extract_file_content(content_section)
                operations.append(PatchOperation("create", file_path, content))

            elif action == "EDIT":
                # Apply diff to existing file
                content = self._apply_diff_to_file(file_path, content_section)
                operations.append(PatchOperation("modify", file_path, content))

            elif action == "DELETE":
                operations.append(PatchOperation("delete", file_path))

        return operations

    def _parse_unified_diff(self, patch_content: str, base_dir: Path) -> List[PatchOperation]:
        """Parse standard unified diff format"""
        operations = []

        # Use difflib to parse unified diff
        lines = patch_content.splitlines()
        current_file = None
        diff_lines = []

        for line in lines:
            if line.startswith('--- '):
                if current_file and diff_lines:
                    # Process previous file
                    content = self._apply_unified_diff(current_file, diff_lines)
                    operations.append(PatchOperation("modify", current_file, content))

                # Start new file
                file_path = line[4:].strip()
                current_file = Path(base_dir) / file_path
                diff_lines = []

            elif line.startswith('+++ '):
                continue  # Skip +++ lines

            elif current_file:
                diff_lines.append(line)

        # Process last file
        if current_file and diff_lines:
            content = self._apply_unified_diff(current_file, diff_lines)
            operations.append(PatchOperation("modify", current_file, content))

        return operations

    async def apply_operations(self, operations: List[PatchOperation]) -> Dict[str, Any]:
        """Apply patch operations with backup and rollback support"""
        applied_operations = []

        try:
            for operation in operations:
                backup_path = await self._create_backup(operation.file_path)
                operation.backup_path = backup_path

                if operation.operation == "create":
                    await self._create_file(operation.file_path, operation.content)
                elif operation.operation == "modify":
                    await self._modify_file(operation.file_path, operation.content)
                elif operation.operation == "delete":
                    await self._delete_file(operation.file_path)

                applied_operations.append(operation)

            return {
                "success": True,
                "operations_applied": len(applied_operations),
                "message": f"Successfully applied {len(applied_operations)} operations"
            }

        except Exception as e:
            # Rollback on error
            await self._rollback_operations(applied_operations)
            return {
                "success": False,
                "error": str(e),
                "operations_rolled_back": len(applied_operations)
            }

    async def _create_backup(self, file_path: Path) -> Optional[Path]:
        """Create backup of file before modification"""
        if not file_path.exists():
            return None

        backup_name = f"{file_path.name}.backup.{int(time.time())}"
        backup_path = self.backup_dir / backup_name

        try:
            shutil.copy2(file_path, backup_path)
            return backup_path
        except Exception as e:
            logger.warning(f"Failed to create backup for {file_path}: {e}")
            return None

    async def _create_file(self, file_path: Path, content: str) -> None:
        """Create new file with content"""
        file_path.parent.mkdir(parents=True, exist_ok=True)

        async with aiofiles.open(file_path, 'w', encoding='utf-8') as f:
            await f.write(content)

    async def _modify_file(self, file_path: Path, content: str) -> None:
        """Modify existing file with new content"""
        async with aiofiles.open(file_path, 'w', encoding='utf-8') as f:
            await f.write(content)

    async def _delete_file(self, file_path: Path) -> None:
        """Delete file safely"""
        if file_path.exists():
            file_path.unlink()
```

### 2. Shell Command Execution

**File: `src/utils/agent/handle_exec_command.py`**

#### Command Processing Pipeline using Python:
1. **Command Parsing**: Parse shell command syntax using `shlex`
2. **Platform Adaptation**: Convert Unix commands for Windows using `platform`
3. **Security Validation**: Check against approval policies
4. **Working Directory**: Set execution context using `os.chdir` or `subprocess` cwd
5. **Timeout Management**: Prevent hanging processes using `asyncio.wait_for`
6. **Output Capture**: Stream stdout/stderr using `asyncio.subprocess`
7. **Result Formatting**: Structure output for AI consumption

```python
import asyncio
import shlex
import platform
from pathlib import Path
from typing import Dict, List, Any, Optional
import subprocess

class CommandExecutor:
    """Advanced command execution with platform adaptation"""

    def __init__(self):
        self.platform = platform.system().lower()
        self.command_map = self._get_command_map()

    def _get_command_map(self) -> Dict[str, str]:
        """Platform-specific command mapping"""
        if self.platform == "windows":
            return {
                "ls": "dir",
                "grep": "findstr",
                "cat": "type",
                "rm": "del",
                "cp": "copy",
                "mv": "move",
                "which": "where",
                "ps": "tasklist",
                "kill": "taskkill"
            }
        return {}  # Unix systems use original commands

    async def execute_command(self,
                            command: List[str],
                            workdir: Optional[str] = None,
                            timeout: int = 30,
                            capture_output: bool = True) -> Dict[str, Any]:
        """Execute command with platform adaptation and security"""

        # Adapt command for platform
        adapted_command = self._adapt_command(command)

        # Validate command security
        if not self._is_command_safe(adapted_command):
            return {
                "success": False,
                "error": f"Command not allowed: {adapted_command[0]}"
            }

        try:
            # Create subprocess
            if capture_output:
                process = await asyncio.create_subprocess_exec(
                    *adapted_command,
                    cwd=workdir,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                    limit=1024*1024  # 1MB limit
                )
            else:
                process = await asyncio.create_subprocess_exec(
                    *adapted_command,
                    cwd=workdir
                )

            # Wait for completion with timeout
            stdout, stderr = await asyncio.wait_for(
                process.communicate(),
                timeout=timeout
            )

            result = {
                "success": True,
                "command": " ".join(adapted_command),
                "return_code": process.returncode,
                "stdout": stdout.decode('utf-8', errors='replace') if stdout else "",
                "stderr": stderr.decode('utf-8', errors='replace') if stderr else "",
                "workdir": workdir or str(Path.cwd())
            }

            return result

        except asyncio.TimeoutError:
            # Kill process if it's still running
            if process.returncode is None:
                process.kill()
                await process.wait()

            return {
                "success": False,
                "error": f"Command timed out after {timeout} seconds",
                "command": " ".join(adapted_command)
            }

        except FileNotFoundError:
            return {
                "success": False,
                "error": f"Command not found: {adapted_command[0]}",
                "command": " ".join(adapted_command)
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "command": " ".join(adapted_command)
            }

    def _adapt_command(self, command: List[str]) -> List[str]:
        """Adapt Unix commands for Windows platform"""
        if not command or self.platform != "windows":
            return command

        base_command = command[0]
        if base_command in self.command_map:
            adapted = [self.command_map[base_command]] + command[1:]
            return adapted

        return command

    def _is_command_safe(self, command: List[str]) -> bool:
        """Check if command is safe to execute"""
        if not command:
            return False

        dangerous_commands = {
            "rm", "del", "rmdir", "format", "fdisk", "mkfs",
            "sudo", "su", "chmod", "chown", "passwd",
            "shutdown", "reboot", "halt", "poweroff"
        }

        base_command = command[0].lower()

        # Check for dangerous commands
        if base_command in dangerous_commands:
            return False

        # Check for dangerous flags
        dangerous_flags = {"-rf", "--force", "/f", "/q"}
        for arg in command[1:]:
            if arg.lower() in dangerous_flags:
                return False

        return True
```

### 3. Git Integration

#### Git Repository Detection and Operations:
- **Safety Checks**: Warn when outside git repositories using `GitPython`
- **Branch Awareness**: Understand current git state
- **Change Tracking**: Monitor file modifications
- **Diff Generation**: Create git diffs for review

```python
import git
from git import Repo, InvalidGitRepositoryError
from pathlib import Path
from typing import Optional, Dict, Any, List

class GitIntegration:
    """Git repository integration and operations"""

    def __init__(self, repo_path: Optional[Path] = None):
        self.repo_path = repo_path or Path.cwd()
        self.repo = self._get_repository()

    def _get_repository(self) -> Optional[Repo]:
        """Get git repository if available"""
        try:
            return Repo(self.repo_path, search_parent_directories=True)
        except InvalidGitRepositoryError:
            return None

    def is_git_repository(self) -> bool:
        """Check if current directory is in a git repository"""
        return self.repo is not None

    def get_repository_info(self) -> Dict[str, Any]:
        """Get comprehensive repository information"""
        if not self.repo:
            return {"is_git_repo": False}

        try:
            return {
                "is_git_repo": True,
                "current_branch": self.repo.active_branch.name,
                "commit_hash": self.repo.head.commit.hexsha[:8],
                "commit_message": self.repo.head.commit.message.strip(),
                "is_dirty": self.repo.is_dirty(),
                "untracked_files": self.repo.untracked_files,
                "modified_files": [item.a_path for item in self.repo.index.diff(None)],
                "staged_files": [item.a_path for item in self.repo.index.diff("HEAD")],
                "remote_url": self._get_remote_url()
            }
        except Exception as e:
            return {
                "is_git_repo": True,
                "error": f"Failed to get repository info: {e}"
            }

    def get_diff(self, staged: bool = False, file_path: Optional[str] = None) -> str:
        """Generate git diff"""
        if not self.repo:
            return "Not in a git repository"

        try:
            if staged:
                # Diff of staged changes
                diff = self.repo.index.diff("HEAD", paths=file_path)
            else:
                # Diff of working directory changes
                diff = self.repo.index.diff(None, paths=file_path)

            diff_text = ""
            for item in diff:
                diff_text += f"--- a/{item.a_path}\n"
                diff_text += f"+++ b/{item.b_path}\n"
                diff_text += item.diff.decode('utf-8', errors='replace')
                diff_text += "\n"

            return diff_text if diff_text else "No changes found"

        except Exception as e:
            return f"Error generating diff: {e}"

    def get_status(self) -> Dict[str, List[str]]:
        """Get git status information"""
        if not self.repo:
            return {"error": ["Not in a git repository"]}

        try:
            status = {
                "modified": [],
                "added": [],
                "deleted": [],
                "renamed": [],
                "untracked": list(self.repo.untracked_files)
            }

            # Check staged changes
            for item in self.repo.index.diff("HEAD"):
                if item.change_type == 'M':
                    status["modified"].append(item.a_path)
                elif item.change_type == 'A':
                    status["added"].append(item.a_path)
                elif item.change_type == 'D':
                    status["deleted"].append(item.a_path)
                elif item.change_type == 'R':
                    status["renamed"].append(f"{item.a_path} -> {item.b_path}")

            # Check working directory changes
            for item in self.repo.index.diff(None):
                if item.change_type == 'M' and item.a_path not in status["modified"]:
                    status["modified"].append(item.a_path)

            return status

        except Exception as e:
            return {"error": [f"Failed to get status: {e}"]}

    def _get_remote_url(self) -> Optional[str]:
        """Get remote repository URL"""
        try:
            if self.repo.remotes:
                return self.repo.remotes.origin.url
        except:
            pass
        return None

    async def execute_git_command(self, args: List[str]) -> Dict[str, Any]:
        """Execute git command safely"""
        if not args or args[0] != "git":
            return {"success": False, "error": "Not a git command"}

        # Allow only safe git commands
        safe_git_commands = {
            "status", "diff", "log", "show", "branch", "remote",
            "ls-files", "ls-tree", "rev-parse", "describe"
        }

        if len(args) < 2 or args[1] not in safe_git_commands:
            return {
                "success": False,
                "error": f"Git command not allowed: {args[1] if len(args) > 1 else 'none'}"
            }

        # Execute using CommandExecutor
        executor = CommandExecutor()
        return await executor.execute_command(args, workdir=str(self.repo_path))
```

### 4. Project Documentation Analysis

**File: `src/utils/config.py`**

#### Documentation Discovery using Python:
```python
from pathlib import Path
from typing import Optional, List
import markdown
from markdown.extensions import codehilite, fenced_code

class DocumentationAnalyzer:
    """Analyze and integrate project documentation"""

    def __init__(self, start_dir: Path = Path.cwd()):
        self.start_dir = start_dir
        self.markdown_processor = markdown.Markdown(
            extensions=['codehilite', 'fenced_code', 'tables', 'toc']
        )

    def discover_project_doc_path(self) -> Optional[Path]:
        """Search for project documentation files"""
        candidates = [
            "AGENTS.md", "README.md", "docs/README.md",
            "documentation.md", "DOCS.md", "PROJECT.md"
        ]

        current_dir = self.start_dir

        # Search upward through directory tree
        while current_dir != current_dir.parent:
            for candidate in candidates:
                doc_path = current_dir / candidate
                if doc_path.exists():
                    return doc_path
            current_dir = current_dir.parent

        return None

    def load_documentation(self, doc_path: Optional[Path] = None) -> Optional[str]:
        """Load and process documentation content"""
        if doc_path is None:
            doc_path = self.discover_project_doc_path()

        if not doc_path or not doc_path.exists():
            return None

        try:
            with open(doc_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Process markdown to extract key information
            return self._process_documentation(content)

        except Exception as e:
            logger.warning(f"Failed to load documentation from {doc_path}: {e}")
            return None

    def _process_documentation(self, content: str) -> str:
        """Process documentation content for AI context"""
        # Convert markdown to structured text
        html = self.markdown_processor.convert(content)

        # Extract key sections
        sections = self._extract_key_sections(content)

        # Format for AI consumption
        formatted_content = "# Project Documentation\n\n"

        for section_name, section_content in sections.items():
            formatted_content += f"## {section_name}\n\n{section_content}\n\n"

        return formatted_content

    def _extract_key_sections(self, content: str) -> Dict[str, str]:
        """Extract key sections from documentation"""
        sections = {}

        # Split by headers
        lines = content.split('\n')
        current_section = "Overview"
        current_content = []

        for line in lines:
            if line.startswith('#'):
                # Save previous section
                if current_content:
                    sections[current_section] = '\n'.join(current_content).strip()

                # Start new section
                current_section = line.lstrip('#').strip()
                current_content = []
            else:
                current_content.append(line)

        # Save last section
        if current_content:
            sections[current_section] = '\n'.join(current_content).strip()

        return sections

    def get_project_context(self) -> Dict[str, Any]:
        """Get comprehensive project context"""
        context = {
            "documentation": self.load_documentation(),
            "structure": self._analyze_project_structure(),
            "languages": self._detect_languages(),
            "frameworks": self._detect_frameworks()
        }

        return context

    def _analyze_project_structure(self) -> Dict[str, Any]:
        """Analyze project directory structure"""
        structure = {
            "directories": [],
            "files": [],
            "config_files": []
        }

        config_patterns = {
            "*.json", "*.yaml", "*.yml", "*.toml", "*.ini",
            "*.cfg", "Dockerfile", "docker-compose.*",
            "requirements.txt", "pyproject.toml", "setup.py"
        }

        for item in self.start_dir.rglob("*"):
            if item.is_dir() and not item.name.startswith('.'):
                structure["directories"].append(str(item.relative_to(self.start_dir)))
            elif item.is_file():
                rel_path = str(item.relative_to(self.start_dir))
                structure["files"].append(rel_path)

                # Check if it's a config file
                if any(item.match(pattern) for pattern in config_patterns):
                    structure["config_files"].append(rel_path)

        return structure

    def _detect_languages(self) -> List[str]:
        """Detect programming languages used in project"""
        language_extensions = {
            '.py': 'Python',
            '.js': 'JavaScript',
            '.ts': 'TypeScript',
            '.java': 'Java',
            '.cpp': 'C++',
            '.c': 'C',
            '.go': 'Go',
            '.rs': 'Rust',
            '.rb': 'Ruby',
            '.php': 'PHP'
        }

        detected_languages = set()

        for file_path in self.start_dir.rglob("*"):
            if file_path.is_file():
                suffix = file_path.suffix.lower()
                if suffix in language_extensions:
                    detected_languages.add(language_extensions[suffix])

        return list(detected_languages)

    def _detect_frameworks(self) -> List[str]:
        """Detect frameworks and tools used in project"""
        frameworks = []

        # Check for Python frameworks
        if (self.start_dir / "requirements.txt").exists():
            frameworks.append("Python/pip")
        if (self.start_dir / "pyproject.toml").exists():
            frameworks.append("Python/Poetry")
        if (self.start_dir / "setup.py").exists():
            frameworks.append("Python/setuptools")

        # Check for Node.js frameworks
        if (self.start_dir / "package.json").exists():
            frameworks.append("Node.js/npm")

        # Check for other frameworks
        if (self.start_dir / "Dockerfile").exists():
            frameworks.append("Docker")
        if (self.start_dir / "docker-compose.yml").exists():
            frameworks.append("Docker Compose")

        return frameworks
```
